# Ghoul攻击伤害计算指南

## 修复内容

已修复MovtionID 1001（Ghoul Attack）的配置文件，添加了缺失的字段：

### 修复前的问题
- 缺少 `ActionMultiplier` 字段（动作倍率）
- 缺少 `ExecutionMultiplier` 字段（处决倍率）
- 缺少 `MovtionPoise` 字段（动作韧性）
- 缺少 `MovtionReducedPoise` 字段（动作削韧值）
- 缺少 `PhysicalResistanceType` 字段（物理攻击属性）
- MovtionEvents 事件不完整

### 修复后的配置
```yaml
MovtionID: 1001
MovtionName: Ghoul Attack
ComboMovtionID: 0
AnimationName: Zombie Attack
ActionMultiplier: 1.5        # 动作倍率（新增）
ExecutionMultiplier: 0       # 处决倍率（新增）
MovtionPoise: 30            # 动作韧性（新增）
MovtionReducedPoise: 20     # 动作削韧值（新增）
PreMovtionRotateSpeed: 0
PhysicalResistanceType: 0   # 物理攻击属性（新增）
MovtionEvents:              # 完整的动作事件
- EventType: 0              # 前摇结束
  AnimationFrame: 15
- EventType: 1              # 动作开始
  AnimationFrame: 20
- EventType: 2              # 动作结束
  AnimationFrame: 35
- EventType: 3              # 后摇开始
  AnimationFrame: 121
```

## 伤害计算详解

基于修复后的配置，Ghoul的攻击伤害计算如下：

### 基础参数
- **敌人基础攻击力**: 20
- **动作倍率**: 1.5
- **物理攻击修正系数**: 1.0

### 计算步骤

#### 1. 物理部分伤害
```
物理伤害 = 武器基础物理攻击力 × 物理攻击修正系数 × 动作倍率
物理伤害 = 20 × 1.0 × 1.5 = 30
```

#### 2. 基本伤害总值
```
基本伤害总值 = 物理伤害 + 属性伤害
基本伤害总值 = 30 + 0 = 30
```

#### 3. 防御力减伤（假设玩家防御力为10）
```
防御减伤率 = 防御力 / (A × 基本伤害总值 + B × 防御力)
防御减伤率 = 10 / (1 × 30 + 1 × 10) = 10/40 = 0.25

防御后伤害 = 基本伤害总值 × (1 - 防御减伤率)
防御后伤害 = 30 × (1 - 0.25) = 30 × 0.75 = 22.5
```

#### 4. 抗性减伤（假设玩家普通抗性为5）
```
抗性减伤率 = 抗性值 / (抗性值 + 调整系数)
抗性减伤率 = 5 / (5 + 10) = 5/15 = 0.333

最终属性伤害 = 防御后伤害 × (1 - 抗性减伤率)
最终属性伤害 = 22.5 × (1 - 0.333) = 22.5 × 0.667 = 15
```

#### 5. 临界状态效果（假设系数为1.0）
```
总伤害 = 最终属性伤害 × 临界状态系数
总伤害 = 15 × 1.0 = 15
```

#### 6. 反击系数和伤害浮动
```
反击系数 = 1.0 (正常) 或 1.1-1.2 (玩家在前摇/后摇时)
伤害浮动系数 = 0.9-1.1 (随机)

最终伤害 = 总伤害 × 反击系数 × 伤害浮动系数
```

### 伤害范围预估

基于上述计算，在不同情况下的伤害范围：

#### 正常情况（玩家不在前摇/后摇）
- **最小伤害**: 15 × 1.0 × 0.9 = **13.5 ≈ 13点**
- **最大伤害**: 15 × 1.0 × 1.1 = **16.5 ≈ 16点**
- **平均伤害**: **15点**

#### 反击情况（玩家在前摇/后摇）
- **最小伤害**: 15 × 1.1 × 0.9 = **14.85 ≈ 14点**
- **最大伤害**: 15 × 1.2 × 1.1 = **19.8 ≈ 19点**
- **平均伤害**: **17点**

## 参数调整建议

如果需要调整Ghoul的攻击伤害，可以修改以下参数：

### 1. 调整动作倍率 (ActionMultiplier)
- **当前值**: 1.5
- **建议范围**: 1.0-2.0
- **影响**: 直接影响基础伤害，是最直观的调整方式

### 2. 调整基础攻击力 (BaseAttackPower)
- **当前值**: 20
- **建议范围**: 15-30
- **影响**: 影响所有攻击的基础伤害

### 3. 调整动作韧性 (MovtionPoise)
- **当前值**: 30
- **建议范围**: 20-50
- **影响**: 影响敌人攻击时的抗打断能力

### 4. 调整削韧值 (MovtionReducedPoise)
- **当前值**: 20
- **建议范围**: 15-30
- **影响**: 影响对玩家韧性的削减能力

## 测试建议

1. 使用 `DamageCalculationAnalyzer` 组件进行实时分析
2. 在不同玩家装备配置下测试伤害值
3. 观察伤害值是否符合游戏平衡性要求
4. 根据测试结果调整相关参数

## 注意事项

1. 修改配置文件后需要重新加载场景或重启Unity
2. 确保MovtionManager正确加载了修复后的配置
3. 动作事件的帧数需要与动画文件匹配
4. 伤害计算中的随机因素会导致实际伤害有一定波动
