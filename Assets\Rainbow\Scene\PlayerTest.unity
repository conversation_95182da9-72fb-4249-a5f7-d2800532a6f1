%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1001 &195219231
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 6011950260186478652, guid: 4ccd1d4beb5eaa541b441c947429f4b0,
        type: 3}
      propertyPath: m_Name
      value: robote
      objectReference: {fileID: 0}
    - target: {fileID: 6351601889511678598, guid: 4ccd1d4beb5eaa541b441c947429f4b0,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -2.95051
      objectReference: {fileID: 0}
    - target: {fileID: 6351601889511678598, guid: 4ccd1d4beb5eaa541b441c947429f4b0,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.192
      objectReference: {fileID: 0}
    - target: {fileID: 6351601889511678598, guid: 4ccd1d4beb5eaa541b441c947429f4b0,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 1.633
      objectReference: {fileID: 0}
    - target: {fileID: 6351601889511678598, guid: 4ccd1d4beb5eaa541b441c947429f4b0,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 6351601889511678598, guid: 4ccd1d4beb5eaa541b441c947429f4b0,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 6351601889511678598, guid: 4ccd1d4beb5eaa541b441c947429f4b0,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6351601889511678598, guid: 4ccd1d4beb5eaa541b441c947429f4b0,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6351601889511678598, guid: 4ccd1d4beb5eaa541b441c947429f4b0,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 6351601889511678598, guid: 4ccd1d4beb5eaa541b441c947429f4b0,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6351601889511678598, guid: 4ccd1d4beb5eaa541b441c947429f4b0,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 4ccd1d4beb5eaa541b441c947429f4b0, type: 3}
--- !u!43 &437383863
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: <Renderable generated -4682>
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: -12, y: 2, z: 29.5}
      m_Extent: {x: 34, y: 2, z: 31.5}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200000002000300040005000600040006000700080009000a0008000a000b000c000d000e000c000e000f00100011001200100012001300140015001600140016001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 24
      format: 0
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 40
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 1152
    _typelessdata: 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
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: -12, y: 2, z: 29.5}
    m_Extent: {x: 34, y: 2, z: 31.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshMetrics[0]: 1
  m_MeshMetrics[1]: 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1 &509783882
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 509783886}
  - component: {fileID: 509783885}
  - component: {fileID: 509783884}
  - component: {fileID: 509783883}
  m_Layer: 8
  m_Name: Capsule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &509783883
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 509783882}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &509783884
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 509783882}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &509783885
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 509783882}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &509783886
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 509783882}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.153, y: 0.799, z: 1.383}
  m_LocalScale: {x: 0.62, y: 1, z: 0.61}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &576858449
GameObject:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 576858450}
  - component: {fileID: 576858453}
  - component: {fileID: 576858452}
  - component: {fileID: 576858451}
  m_Layer: 0
  m_Name: cm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &576858450
Transform:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 576858449}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 630081068}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &576858451
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 576858449}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fa7155796051b734daa718462081dc5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_BindingMode: 1
  m_FollowOffset: {x: 0, y: 2.18, z: -3.52}
  m_XDamping: 1
  m_YDamping: 1
  m_ZDamping: 1
  m_AngularDampingMode: 0
  m_PitchDamping: 0
  m_YawDamping: 0
  m_RollDamping: 0
  m_AngularDamping: 0
--- !u!114 &576858452
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 576858449}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4044717213e31446939f7bd49c896ea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TrackedObjectOffset: {x: 0, y: 0, z: 0}
  m_LookaheadTime: 0
  m_LookaheadSmoothing: 0
  m_LookaheadIgnoreY: 0
  m_HorizontalDamping: 0.5
  m_VerticalDamping: 0.5
  m_ScreenX: 0.5
  m_ScreenY: 0.5
  m_DeadZoneWidth: 0
  m_DeadZoneHeight: 0
  m_SoftZoneWidth: 0.8
  m_SoftZoneHeight: 0.8
  m_BiasX: 0
  m_BiasY: 0
  m_CenterOnActivate: 1
--- !u!114 &576858453
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 576858449}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac0b09e7857660247b1477e93731de29, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &630081066
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 630081068}
  - component: {fileID: 630081067}
  m_Layer: 0
  m_Name: Virtual Camera
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &630081067
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 630081066}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45e653bab7fb20e499bda25e1b646fea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ExcludedPropertiesInInspector:
  - m_Script
  m_LockStageInInspector: 
  m_StreamingVersion: 20170927
  m_Priority: 10
  m_StandbyUpdate: 2
  m_LookAt: {fileID: 2039935952}
  m_Follow: {fileID: 1675357203}
  m_Lens:
    FieldOfView: 60
    OrthographicSize: 5
    NearClipPlane: 0.3
    FarClipPlane: 1000
    Dutch: 0
    ModeOverride: 0
    LensShift: {x: 0, y: 0}
    GateFit: 2
    FocusDistance: 10
    m_SensorSize: {x: 1, y: 1}
  m_Transitions:
    m_BlendHint: 0
    m_InheritPosition: 0
    m_OnCameraLive:
      m_PersistentCalls:
        m_Calls: []
  m_LegacyBlendHint: 0
  m_ComponentOwner: {fileID: 576858450}
--- !u!4 &630081068
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 630081066}
  serializedVersion: 2
  m_LocalRotation: {x: 0.12444175, y: 0.000000025505086, z: -0.0000000031987617, w: 0.99222696}
  m_LocalPosition: {x: 0.372, y: 2.18, z: -3.07}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 576858450}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &742561189
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 742561191}
  - component: {fileID: 742561190}
  - component: {fileID: 742561192}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &742561190
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 742561189}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 1
  m_Shape: 0
  m_Color: {r: 1, g: 0.95686275, b: 0.8392157, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!4 &742561191
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 742561189}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!114 &742561192
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 742561189}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!1 &762296520
GameObject:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 762296521}
  - component: {fileID: 762296522}
  m_Layer: 0
  m_Name: '[generated-meshes]'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &762296521
Transform:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 762296520}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 819880705}
  - {fileID: 1879631934}
  m_Father: {fileID: 2023603159}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &762296522
MonoBehaviour:
  m_ObjectHideFlags: 27
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 762296520}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5c3643d4978096d4880d7edbf4f65a50, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Version: 1
  owner: {fileID: 2023603158}
  helperSurfaces:
  - SharedMesh: {fileID: 1799504206}
    RenderSurfaceType: 4
    HasGeneratedNormals: 0
    MeshDescription:
      meshQuery:
        layers: 33554440
        maskAndChannels: 8
      surfaceParameter: 28354
      meshQueryIndex: 1
      subMeshQueryIndex: 0
      geometryHashValue: 5513869603323557304
      surfaceHashValue: -6288709014872426271
      vertexCount: 24
      indexCount: 36
    GameObject: {fileID: 0}
    MeshFilter: {fileID: 0}
    MeshRenderer: {fileID: 0}
  - SharedMesh: {fileID: 2119401593}
    RenderSurfaceType: 6
    HasGeneratedNormals: 0
    MeshDescription:
      meshQuery:
        layers: 2
        maskAndChannels: 2
      surfaceParameter: 0
      meshQueryIndex: 2
      subMeshQueryIndex: 0
      geometryHashValue: 5513869603323557304
      surfaceHashValue: -6288709014872426271
      vertexCount: 24
      indexCount: 36
    GameObject: {fileID: 0}
    MeshFilter: {fileID: 0}
    MeshRenderer: {fileID: 0}
  - SharedMesh: {fileID: 1029059248}
    RenderSurfaceType: 7
    HasGeneratedNormals: 0
    MeshDescription:
      meshQuery:
        layers: 4
        maskAndChannels: 4
      surfaceParameter: 0
      meshQueryIndex: 3
      subMeshQueryIndex: 0
      geometryHashValue: 5513869603323557304
      surfaceHashValue: -6288709014872426271
      vertexCount: 24
      indexCount: 36
    GameObject: {fileID: 0}
    MeshFilter: {fileID: 0}
    MeshRenderer: {fileID: 0}
--- !u!1 &819880704
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 819880705}
  - component: {fileID: 819880708}
  - component: {fileID: 819880707}
  - component: {fileID: 819880706}
  m_Layer: 4
  m_Name: '[generated-render-mesh]'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 124
  m_IsActive: 1
--- !u!4 &819880705
Transform:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 819880704}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 762296521}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &819880706
MeshRenderer:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 819880704}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0f4460a046b285b44b3ad315b5d6dead, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 2
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &819880707
MeshFilter:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 819880704}
  m_Mesh: {fileID: 437383863}
--- !u!114 &819880708
MonoBehaviour:
  m_ObjectHideFlags: 16
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 819880704}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76a92adfdc6e3524fb82e0817c421ae3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Version: 1
  SharedMesh: {fileID: 437383863}
  RenderMaterial: {fileID: 2100000, guid: 0f4460a046b285b44b3ad315b5d6dead, type: 2}
  PhysicsMaterial: {fileID: 0}
  RenderSurfaceType: 0
  MeshDescription:
    meshQuery:
      layers: 16777223
      maskAndChannels: 234881031
    surfaceParameter: 30832
    meshQueryIndex: 0
    subMeshQueryIndex: 0
    geometryHashValue: -4062489993385677889
    surfaceHashValue: 7603686644028939581
    vertexCount: 24
    indexCount: 36
  HasGeneratedNormals: 0
  HasUV2: 0
  LightingHashValue: 0
--- !u!1 &965065731
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 965065732}
  m_Layer: 0
  m_Name: GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &965065732
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 965065731}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0.432, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 747509071538323304}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &973939540
GameObject:
  m_ObjectHideFlags: 17
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 973939543}
  - component: {fileID: 973939542}
  - component: {fileID: 973939541}
  m_Layer: 0
  m_Name: '[default-CSGModel]'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 124
  m_IsActive: 1
--- !u!54 &973939541
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973939540}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 126
  m_CollisionDetection: 0
--- !u!114 &973939542
MonoBehaviour:
  m_ObjectHideFlags: 17
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973939540}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8672dcc90e16b0a489a41edf56e016f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  PrefabBehaviour: 0
  PrefabSourceAlignment: 4
  PrefabDestinationAlignment: 0
  Version: 1.1
  infiniteBrush: {fileID: 0}
  Settings: 1025
  VertexChannels: 14
  ReceiveGI: 2
  MeshColliderCookingOptions: 14
  ShowGeneratedMeshes: 0
  DefaultPhysicsMaterial: {fileID: 13400000, guid: 0c5fdfb44d6f19646a41a0720fbf751a,
    type: 2}
  exportType: 0
  originType: 0
  exportColliders: 0
  exportPath: 
  angleError: 1
  areaError: 1
  hardAngle: 60
  packMargin: 20
  scaleInLightmap: 1
  autoUVMaxDistance: 0.5
  autoUVMaxAngle: 89
  minimumChartSize: 4
--- !u!4 &973939543
Transform:
  m_ObjectHideFlags: 17
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973939540}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2090329832}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1002114326
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1002114327}
  - component: {fileID: 1002114328}
  m_Layer: 0
  m_Name: Brush (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1002114327
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1002114326}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -12, y: 0, z: 29.5}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2023603159}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1002114328
MonoBehaviour:
  m_ObjectHideFlags: 16
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1002114326}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 218a3c5228e87b54f8102630bb1bfc1d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  PrefabBehaviour: 0
  PrefabSourceAlignment: 4
  PrefabDestinationAlignment: 0
  Version: 2.1
  OperationType: 0
  Flags: 0
  Shape:
    Version: 1
    Surfaces:
    - Plane:
        a: 0.0000000069027437
        b: 0
        c: -1
        d: 31.5
      Tangent: {x: -1, y: -0, z: -0.0000000069027437}
      BiNormal: {x: -0, y: 1, z: 0}
      TexGenIndex: 0
    - Plane:
        a: -1
        b: 0
        c: 0
        d: 34
      Tangent: {x: 0, y: 0, z: 1}
      BiNormal: {x: 0, y: 1, z: -0}
      TexGenIndex: 1
    - Plane:
        a: -0.0000000069027437
        b: 0
        c: 1
        d: 31.5
      Tangent: {x: 1, y: 0, z: 0.0000000069027437}
      BiNormal: {x: 0, y: 1, z: -0}
      TexGenIndex: 2
    - Plane:
        a: 1
        b: 0
        c: 0
        d: 34
      Tangent: {x: 0, y: 0, z: -1}
      BiNormal: {x: -0, y: 1, z: 0}
      TexGenIndex: 3
    - Plane:
        a: 0
        b: 1
        c: 0
        d: 4
      Tangent: {x: 1, y: 0, z: 0}
      BiNormal: {x: 0, y: 0, z: -1}
      TexGenIndex: 4
    - Plane:
        a: 0
        b: -1
        c: 0
        d: 0.000000059604645
      Tangent: {x: -1, y: 0, z: 0}
      BiNormal: {x: -0, y: -0, z: -1}
      TexGenIndex: 5
    TexGens:
    - Translation: {x: 0, y: 0}
      Scale: {x: 1, y: 1}
      RotationAngle: 0
      RenderMaterial: {fileID: 2100000, guid: 0f4460a046b285b44b3ad315b5d6dead, type: 2}
      PhysicsMaterial: {fileID: 0}
      SmoothingGroup: 0
    - Translation: {x: 0, y: 0}
      Scale: {x: 1, y: 1}
      RotationAngle: 0
      RenderMaterial: {fileID: 2100000, guid: 0f4460a046b285b44b3ad315b5d6dead, type: 2}
      PhysicsMaterial: {fileID: 0}
      SmoothingGroup: 0
    - Translation: {x: 0, y: 0}
      Scale: {x: 1, y: 1}
      RotationAngle: 0
      RenderMaterial: {fileID: 2100000, guid: 0f4460a046b285b44b3ad315b5d6dead, type: 2}
      PhysicsMaterial: {fileID: 0}
      SmoothingGroup: 0
    - Translation: {x: 0, y: 0}
      Scale: {x: 1, y: 1}
      RotationAngle: 0
      RenderMaterial: {fileID: 2100000, guid: 0f4460a046b285b44b3ad315b5d6dead, type: 2}
      PhysicsMaterial: {fileID: 0}
      SmoothingGroup: 0
    - Translation: {x: 0, y: 0}
      Scale: {x: 1, y: 1}
      RotationAngle: 0
      RenderMaterial: {fileID: 2100000, guid: 0f4460a046b285b44b3ad315b5d6dead, type: 2}
      PhysicsMaterial: {fileID: 0}
      SmoothingGroup: 0
    - Translation: {x: 0, y: 0}
      Scale: {x: 1, y: 1}
      RotationAngle: 0
      RenderMaterial: {fileID: 2100000, guid: 0f4460a046b285b44b3ad315b5d6dead, type: 2}
      PhysicsMaterial: {fileID: 0}
      SmoothingGroup: 0
    TexGenFlags: 010000000100000001000000010000000100000001000000
    Materials: []
  ControlMesh:
    Vertices:
    - {x: -34, y: 4, z: -31.5}
    - {x: -34, y: 4, z: 31.5}
    - {x: 34, y: 4, z: 31.5}
    - {x: 34, y: 4, z: -31.5}
    - {x: 34, y: -0.000000059604645, z: -31.5}
    - {x: -34, y: -0.000000059604645, z: -31.5}
    - {x: -34, y: -0.000000059604645, z: 31.5}
    - {x: 34, y: -0.000000059604645, z: 31.5}
    Edges:
    - TwinIndex: 8
      PolygonIndex: 4
      HardEdge: 1
      VertexIndex: 0
    - TwinIndex: 12
      PolygonIndex: 4
      HardEdge: 1
      VertexIndex: 1
    - TwinIndex: 16
      PolygonIndex: 4
      HardEdge: 1
      VertexIndex: 2
    - TwinIndex: 20
      PolygonIndex: 4
      HardEdge: 1
      VertexIndex: 3
    - TwinIndex: 10
      PolygonIndex: 5
      HardEdge: 1
      VertexIndex: 4
    - TwinIndex: 14
      PolygonIndex: 5
      HardEdge: 1
      VertexIndex: 5
    - TwinIndex: 18
      PolygonIndex: 5
      HardEdge: 1
      VertexIndex: 6
    - TwinIndex: 22
      PolygonIndex: 5
      HardEdge: 1
      VertexIndex: 7
    - TwinIndex: 0
      PolygonIndex: 0
      HardEdge: 1
      VertexIndex: 3
    - TwinIndex: 23
      PolygonIndex: 0
      HardEdge: 1
      VertexIndex: 4
    - TwinIndex: 4
      PolygonIndex: 0
      HardEdge: 1
      VertexIndex: 5
    - TwinIndex: 13
      PolygonIndex: 0
      HardEdge: 1
      VertexIndex: 0
    - TwinIndex: 1
      PolygonIndex: 1
      HardEdge: 1
      VertexIndex: 0
    - TwinIndex: 11
      PolygonIndex: 1
      HardEdge: 1
      VertexIndex: 5
    - TwinIndex: 5
      PolygonIndex: 1
      HardEdge: 1
      VertexIndex: 6
    - TwinIndex: 17
      PolygonIndex: 1
      HardEdge: 1
      VertexIndex: 1
    - TwinIndex: 2
      PolygonIndex: 2
      HardEdge: 1
      VertexIndex: 1
    - TwinIndex: 15
      PolygonIndex: 2
      HardEdge: 1
      VertexIndex: 6
    - TwinIndex: 6
      PolygonIndex: 2
      HardEdge: 1
      VertexIndex: 7
    - TwinIndex: 21
      PolygonIndex: 2
      HardEdge: 1
      VertexIndex: 2
    - TwinIndex: 3
      PolygonIndex: 3
      HardEdge: 1
      VertexIndex: 2
    - TwinIndex: 19
      PolygonIndex: 3
      HardEdge: 1
      VertexIndex: 7
    - TwinIndex: 7
      PolygonIndex: 3
      HardEdge: 1
      VertexIndex: 4
    - TwinIndex: 9
      PolygonIndex: 3
      HardEdge: 1
      VertexIndex: 3
    Polygons:
    - EdgeIndices: 08000000090000000a0000000b000000
      TexGenIndex: 0
    - EdgeIndices: 0c0000000d0000000e0000000f000000
      TexGenIndex: 1
    - EdgeIndices: 10000000110000001200000013000000
      TexGenIndex: 2
    - EdgeIndices: 14000000150000001600000017000000
      TexGenIndex: 3
    - EdgeIndices: 00000000010000000200000003000000
      TexGenIndex: 4
    - EdgeIndices: 07000000060000000500000004000000
      TexGenIndex: 5
    Generation: 104
--- !u!43 &1029059248
Mesh:
  m_ObjectHideFlags: 16
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: <ReceiveShadows helper surface generated -4696>
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: -26.5, y: 0.5, z: 16.5}
      m_Extent: {x: 25.5, y: 0.5, z: 20.5}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200000002000300040005000600040006000700080009000a0008000a000b000c000d000e000c000e000f00100011001200100012001300140015001600140016001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 288
    _typelessdata: 000080bf0000803f000080c0000080bf00000000000080c0000050c200000000000080c0000050c20000803f000080c0000050c20000803f00001442000050c20000000000001442000080bf0000000000001442000080bf0000803f00001442000080bf0000803f00001442000080bf0000000000001442000080bf00000000000080c0000080bf0000803f000080c0000050c20000803f000080c0000050c200000000000080c0000050c20000000000001442000050c20000803f00001442000050c20000803f000080c0000050c20000803f00001442000080bf0000803f00001442000080bf0000803f000080c0000080bf0000000000001442000050c20000000000001442000050c200000000000080c0000080bf00000000000080c0
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: -26.5, y: 0.5, z: 16.5}
    m_Extent: {x: 25.5, y: 0.5, z: 20.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshMetrics[0]: 1
  m_MeshMetrics[1]: 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1 &1109713183
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1109713187}
  - component: {fileID: 1109713186}
  - component: {fileID: 1109713185}
  - component: {fileID: 1109713184}
  m_Layer: 8
  m_Name: Capsule (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &1109713184
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1109713183}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1109713185
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1109713183}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1109713186
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1109713183}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1109713187
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1109713183}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.742, y: 0.799, z: 1.383}
  m_LocalScale: {x: 0.62, y: 1, z: 0.61}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1269507150
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.9
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.205
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 2.008
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.6662778
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.6662781
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.23679891
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.23679885
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -39.131
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f,
        type: 3}
      propertyPath: m_Name
      value: robote
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: d3dd0140ff1a14c4a8f1d35a8c3a685f, type: 3}
--- !u!1 &1400422426
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1400422427}
  - component: {fileID: 1400422429}
  - component: {fileID: 1400422428}
  m_Layer: 0
  m_Name: Md_Straight_Sword_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1400422427
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1400422426}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6822207, y: -0.0013259053, z: 0.0149476975, w: -0.7309924}
  m_LocalPosition: {x: -0.000218, y: 0.001333, z: 0.000181}
  m_LocalScale: {x: 0.010000001, y: 0.010000001, z: 0.010000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1576521595}
  m_Father: {fileID: 1741070636}
  m_LocalEulerAnglesHint: {x: 0, y: 86.022, z: -91.655}
--- !u!23 &1400422428
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1400422426}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 8748892135801146194, guid: 6aca1f6740cb34547b89b9013d0e95b3, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1400422429
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1400422426}
  m_Mesh: {fileID: -4019297881887596419, guid: 6aca1f6740cb34547b89b9013d0e95b3, type: 3}
--- !u!1 &1576521594
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1576521595}
  m_Layer: 0
  m_Name: GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1576521595
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1576521594}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0.432, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1400422427}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1675357202
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1675357203}
  m_Layer: 0
  m_Name: Player
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1675357203
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1675357202}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.372, y: 0, z: 0.45}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 645631431106755152}
  - {fileID: 2039935952}
  - {fileID: 1741070635}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!43 &1700708083
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: <Collider generated -4690>
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: -12, y: 2, z: 29.5}
      m_Extent: {x: 34, y: 2, z: 31.5}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200000002000300040005000600040006000700080009000a0008000a000b000c000d000e000c000e000f00100011001200100012001300140015001600140016001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 288
    _typelessdata: 0000b04100008040000000c00000b041000080b3000000c0000038c2000080b3000000c0000038c200008040000000c0000038c20000804000007442000038c2000080b3000074420000b041000080b3000074420000b04100008040000074420000b04100008040000074420000b041000080b3000074420000b041000080b3000000c00000b04100008040000000c0000038c200008040000000c0000038c2000080b3000000c0000038c2000080b300007442000038c20000804000007442000038c200008040000000c0000038c200008040000074420000b04100008040000074420000b04100008040000000c00000b041000080b300007442000038c2000080b300007442000038c2000080b3000000c00000b041000080b3000000c0
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: -12, y: 2, z: 29.5}
    m_Extent: {x: 34, y: 2, z: 31.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 14
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshMetrics[0]: 1
  m_MeshMetrics[1]: 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1001 &1741070634
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1675357203}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      propertyPath: m_Name
      value: CharacterBaseMesh_v10.0_VRC
      objectReference: {fileID: 0}
    - target: {fileID: 5866666021909216657, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      propertyPath: m_Controller
      value: 
      objectReference: {fileID: 9100000, guid: a306df631ccecf346ba3d17bb8b38325, type: 2}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 2084502881125625889, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1400422427}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: ea2916736168f45479b110b080aaad2d,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1741070638}
  m_SourcePrefab: {fileID: 100100000, guid: ea2916736168f45479b110b080aaad2d, type: 3}
--- !u!4 &1741070635 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: ea2916736168f45479b110b080aaad2d,
    type: 3}
  m_PrefabInstance: {fileID: 1741070634}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1741070636 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 2084502881125625889, guid: ea2916736168f45479b110b080aaad2d,
    type: 3}
  m_PrefabInstance: {fileID: 1741070634}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1741070637 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: ea2916736168f45479b110b080aaad2d,
    type: 3}
  m_PrefabInstance: {fileID: 1741070634}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1741070638
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1741070637}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50afe1db18b7a6d439ffda66a94e90c7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  cube:
    editorMode: 1
    openGizmos: 1
    GizmosType: 0
    point: {fileID: 1576521595}
    radius: 0
    size: {x: 0.1, y: 0.91, z: 0.1}
    offSet: {x: 0, y: 0, z: 0}
    gizmosColor: {r: 0.2090857, g: 1, b: 0, a: 1}
  sphere:
    editorMode: 0
    openGizmos: 0
    GizmosType: 1
    point: {fileID: 965065732}
    radius: 0.13
    size: {x: 0, y: 0, z: 0}
    offSet: {x: 0, y: 0, z: 0}
    gizmosColor: {r: 1, g: 0, b: 0, a: 1}
  capsule:
    editorMode: 0
    openGizmos: 0
    GizmosType: 0
    point: {fileID: 0}
    radius: 0
    size: {x: 0, y: 0, z: 0}
    offSet: {x: 0, y: 0, z: 0}
    gizmosColor: {r: 0, g: 0, b: 0, a: 0}
  layer:
    serializedVersion: 2
    m_Bits: 256
--- !u!1 &1755795512
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1755795515}
  - component: {fileID: 1755795514}
  - component: {fileID: 1755795513}
  - component: {fileID: 1755795516}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1755795513
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1755795512}
  m_Enabled: 1
--- !u!20 &1755795514
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1755795512}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1755795515
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1755795512}
  serializedVersion: 2
  m_LocalRotation: {x: 0.124441765, y: 0.00000002550509, z: -0.0000000031987624, w: 0.99222696}
  m_LocalPosition: {x: 0.372, y: 2.18, z: -3.07}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1755795516
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1755795512}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 72ece51f2901e7445ab60da3685d6b5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ShowDebugText: 0
  m_ShowCameraFrustum: 1
  m_IgnoreTimeScale: 0
  m_WorldUpOverride: {fileID: 0}
  m_UpdateMethod: 2
  m_BlendUpdateMethod: 1
  m_DefaultBlend:
    m_Style: 1
    m_Time: 2
    m_CustomCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  m_CustomBlends: {fileID: 0}
  m_CameraCutEvent:
    m_PersistentCalls:
      m_Calls: []
  m_CameraActivatedEvent:
    m_PersistentCalls:
      m_Calls: []
--- !u!43 &1799504206
Mesh:
  m_ObjectHideFlags: 16
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: <Collider helper surface generated -4692>
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: -26.5, y: 0.5, z: 16.5}
      m_Extent: {x: 25.5, y: 0.5, z: 20.5}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200000002000300040005000600040006000700080009000a0008000a000b000c000d000e000c000e000f00100011001200100012001300140015001600140016001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 288
    _typelessdata: 000080bf0000803f000080c0000080bf00000000000080c0000050c200000000000080c0000050c20000803f000080c0000050c20000803f00001442000050c20000000000001442000080bf0000000000001442000080bf0000803f00001442000080bf0000803f00001442000080bf0000000000001442000080bf00000000000080c0000080bf0000803f000080c0000050c20000803f000080c0000050c200000000000080c0000050c20000000000001442000050c20000803f00001442000050c20000803f000080c0000050c20000803f00001442000080bf0000803f00001442000080bf0000803f000080c0000080bf0000000000001442000050c20000000000001442000050c200000000000080c0000080bf00000000000080c0
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: -26.5, y: 0.5, z: 16.5}
    m_Extent: {x: 25.5, y: 0.5, z: 20.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshMetrics[0]: 1
  m_MeshMetrics[1]: 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!1001 &1820596739
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -4.4510827
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.00000011920929
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 1.3735988
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: cfc75180a8cfdca46ad62e7ec8c6f071,
        type: 3}
      propertyPath: m_Name
      value: Mo1ArmorMd010071 #449210
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: cfc75180a8cfdca46ad62e7ec8c6f071, type: 3}
--- !u!1 &1879631933
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1879631934}
  - component: {fileID: 1879631936}
  - component: {fileID: 1879631935}
  m_Layer: 4
  m_Name: '[generated-collider-mesh]'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1879631934
Transform:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1879631933}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 762296521}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!64 &1879631935
MeshCollider:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1879631933}
  m_Material: {fileID: 13400000, guid: 0c5fdfb44d6f19646a41a0720fbf751a, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 14
  m_Mesh: {fileID: 1700708083}
--- !u!114 &1879631936
MonoBehaviour:
  m_ObjectHideFlags: 16
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1879631933}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76a92adfdc6e3524fb82e0817c421ae3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Version: 1
  SharedMesh: {fileID: 1700708083}
  RenderMaterial: {fileID: 0}
  PhysicsMaterial: {fileID: 13400000, guid: 0c5fdfb44d6f19646a41a0720fbf751a, type: 2}
  RenderSurfaceType: 4
  MeshDescription:
    meshQuery:
      layers: 33554440
      maskAndChannels: 8
    surfaceParameter: 30480
    meshQueryIndex: 1
    subMeshQueryIndex: 0
    geometryHashValue: -4062489993385677889
    surfaceHashValue: -6288709014872426271
    vertexCount: 24
    indexCount: 36
  HasGeneratedNormals: 0
  HasUV2: 0
  LightingHashValue: -4062489993385677889
--- !u!1 &2023603156
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2023603159}
  - component: {fileID: 2023603158}
  - component: {fileID: 2023603157}
  m_Layer: 4
  m_Name: Model
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 124
  m_IsActive: 1
--- !u!54 &2023603157
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2023603156}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 126
  m_CollisionDetection: 0
--- !u!114 &2023603158
MonoBehaviour:
  m_ObjectHideFlags: 16
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2023603156}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8672dcc90e16b0a489a41edf56e016f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  PrefabBehaviour: 0
  PrefabSourceAlignment: 4
  PrefabDestinationAlignment: 0
  Version: 1.1
  infiniteBrush: {fileID: 0}
  Settings: 1025
  VertexChannels: 14
  ReceiveGI: 2
  MeshColliderCookingOptions: 14
  ShowGeneratedMeshes: 0
  DefaultPhysicsMaterial: {fileID: 13400000, guid: 0c5fdfb44d6f19646a41a0720fbf751a,
    type: 2}
  exportType: 0
  originType: 0
  exportColliders: 0
  exportPath: 
  angleError: 1
  areaError: 1
  hardAngle: 60
  packMargin: 20
  scaleInLightmap: 1
  autoUVMaxDistance: 0.5
  autoUVMaxAngle: 89
  minimumChartSize: 4
--- !u!4 &2023603159
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2023603156}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1, y: -4, z: -30}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1002114327}
  - {fileID: 762296521}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2039935951
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2039935952}
  m_Layer: 0
  m_Name: CameraPoint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2039935952
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2039935951}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.2829571, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1675357203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2090329831
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2090329832}
  - component: {fileID: 2090329833}
  m_Layer: 0
  m_Name: '[generated-meshes]'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2090329832
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2090329831}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 973939543}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2090329833
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2090329831}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5c3643d4978096d4880d7edbf4f65a50, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Version: 1
  owner: {fileID: 973939542}
  helperSurfaces: []
--- !u!43 &2119401593
Mesh:
  m_ObjectHideFlags: 16
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: <CastShadows helper surface generated -4694>
  serializedVersion: 11
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 24
    localAABB:
      m_Center: {x: -26.5, y: 0.5, z: 16.5}
      m_Extent: {x: 25.5, y: 0.5, z: 20.5}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200000002000300040005000600040006000700080009000a0008000a000b000c000d000e000c000e000f00100011001200100012001300140015001600140016001700
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 24
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 288
    _typelessdata: 000080bf0000803f000080c0000080bf00000000000080c0000050c200000000000080c0000050c20000803f000080c0000050c20000803f00001442000050c20000000000001442000080bf0000000000001442000080bf0000803f00001442000080bf0000803f00001442000080bf0000000000001442000080bf00000000000080c0000080bf0000803f000080c0000050c20000803f000080c0000050c200000000000080c0000050c20000000000001442000050c20000803f00001442000050c20000803f000080c0000050c20000803f00001442000080bf0000803f00001442000080bf0000803f000080c0000080bf0000000000001442000050c20000000000001442000050c200000000000080c0000080bf00000000000080c0
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: -26.5, y: 0.5, z: 16.5}
    m_Extent: {x: 25.5, y: 0.5, z: 20.5}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshMetrics[0]: 1
  m_MeshMetrics[1]: 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!4 &23587686796019594
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 392779363145270819}
  serializedVersion: 2
  m_LocalRotation: {x: -0.46203893, y: 0.53527564, z: 0.46203893, w: 0.53527564}
  m_LocalPosition: {x: 0.006935013, y: 0.035117745, z: 3.2494063e-16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1282943702535506445}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &77826898412709330
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 747509071538323304}
  - component: {fileID: 4463194907307372749}
  - component: {fileID: 1879271353928090631}
  m_Layer: 0
  m_Name: Md_Straight_Sword_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &116487904040327032
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3374304926710103320}
  m_Layer: 0
  m_Name: IndexFinger_02_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &219327263403930985
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6461063800580787695}
  serializedVersion: 2
  m_LocalRotation: {x: -0.018149227, y: -0.07147895, z: 0.09251083, w: 0.9929769}
  m_LocalPosition: {x: 0.035133243, y: -0.00000019978481, z: 0.000000014853608}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3696948010103832235}
  m_Father: {fileID: 6266421766440592846}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &269775009864589546
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 645631431106755152}
  - component: {fileID: 645631431106755153}
  - component: {fileID: 645631431106755154}
  m_Layer: 0
  m_Name: Md_Char_Low_Poly_Man
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &322092966876731958
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2577278881341513857}
  serializedVersion: 2
  m_LocalRotation: {x: -7.711608e-14, y: -0.7071068, z: -7.711608e-14, w: 0.7071068}
  m_LocalPosition: {x: 0.0729575, y: 1.0935696e-16, z: -5.3290704e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6507272962644515495}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &392779363145270819
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 23587686796019594}
  m_Layer: 0
  m_Name: Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &436700650520038016
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1575069896421504540}
  m_Layer: 0
  m_Name: IndexFinger_04_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &586383688928304559
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4739525974870579075}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0016838327, y: 0.040749595, z: -0.041251875, w: 0.99831605}
  m_LocalPosition: {x: 0.3394746, y: 0.0014475863, z: 0.0014362636}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1273017298686451666}
  m_Father: {fileID: 2895030391090505734}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &601759044668655081
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3027290942696129956}
  serializedVersion: 2
  m_LocalRotation: {x: -0.004519649, y: -0.028039929, z: -0.15906802, w: 0.986859}
  m_LocalPosition: {x: 0.037793327, y: -0.0000052657088, z: -0.0000001550738}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7785692942529344552}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &645631431106755152
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 269775009864589546}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 658707787793787005}
  - {fileID: 7927739812710517610}
  m_Father: {fileID: 1675357203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &645631431106755153
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 269775009864589546}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: 6e2f485a5cf07ed4ca20959e516193ce, type: 3}
  m_Controller: {fileID: 9100000, guid: 411728ec8fb1fde45b8b971451115f05, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &645631431106755154
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 269775009864589546}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50afe1db18b7a6d439ffda66a94e90c7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  cube:
    editorMode: 1
    openGizmos: 1
    GizmosType: 0
    point: {fileID: 965065732}
    radius: 0
    size: {x: 0.1, y: 0.91, z: 0.1}
    offSet: {x: 0, y: 0, z: 0}
    gizmosColor: {r: 0.2090857, g: 1, b: 0, a: 1}
  sphere:
    editorMode: 0
    openGizmos: 0
    GizmosType: 1
    point: {fileID: 965065732}
    radius: 0.13
    size: {x: 0, y: 0, z: 0}
    offSet: {x: 0, y: 0, z: 0}
    gizmosColor: {r: 1, g: 0, b: 0, a: 1}
  capsule:
    editorMode: 0
    openGizmos: 0
    GizmosType: 0
    point: {fileID: 0}
    radius: 0
    size: {x: 0, y: 0, z: 0}
    offSet: {x: 0, y: 0, z: 0}
    gizmosColor: {r: 0, g: 0, b: 0, a: 0}
  layer:
    serializedVersion: 2
    m_Bits: 256
--- !u!4 &658707787793787005
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4568206426714299646}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0.008}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 645631431106755152}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &747509071538323304
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 77826898412709330}
  serializedVersion: 2
  m_LocalRotation: {x: -0.48925918, y: 0.47532994, z: -0.52446336, w: 0.50953424}
  m_LocalPosition: {x: 0.092, y: 0.033, z: -0.023}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 965065732}
  m_Father: {fileID: 1273017298686451666}
  m_LocalEulerAnglesHint: {x: 0, y: 86.022, z: -91.655}
--- !u!4 &777791358945769269
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6402179846189689603}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6443777, y: 0.02446273, z: -0.0047377087, w: 0.76430136}
  m_LocalPosition: {x: -0.39930904, y: -3.5527136e-17, z: 3.6415315e-16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3689583888566845469}
  m_Father: {fileID: 2354518038949576080}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &892471014829439350
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1938132023569678010}
  m_Layer: 0
  m_Name: Shield_Attachment_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &981239728439243550
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5384794822977441191}
  m_Layer: 0
  m_Name: Finger_04_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1075521387346685248
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2246004350870409590}
  serializedVersion: 2
  m_LocalRotation: {x: -0.036403257, y: -0.15203469, z: -0.22999534, w: 0.9605532}
  m_LocalPosition: {x: -0.056655698, y: 5.684342e-16, z: -2.842171e-16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2045972862365378719}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1101383286309094527
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8194495803684885590}
  m_Layer: 0
  m_Name: Toes_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1105081612389238255
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1959716783922076753}
  m_Layer: 0
  m_Name: Shoulder_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1160559164006671951
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3279322770728710326}
  m_Layer: 0
  m_Name: Spine_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1205432933545205584
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2895030391090505734}
  m_Layer: 0
  m_Name: Shoulder_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1273017298686451666
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6325059333875814676}
  serializedVersion: 2
  m_LocalRotation: {x: -4.581713e-17, y: -0.035830766, z: -1.2703234e-15, w: 0.9993579}
  m_LocalPosition: {x: 0.2711449, y: -8.5265126e-16, z: 0.00038136088}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6520780891428513789}
  - {fileID: 5411321375219648116}
  - {fileID: 1282630171075302168}
  - {fileID: 7985036018500580370}
  - {fileID: 747509071538323304}
  m_Father: {fileID: 586383688928304559}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1282630171075302168
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7120936847584186934}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: -4.440892e-18}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1273017298686451666}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1282943702535506445
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6439038932026241779}
  serializedVersion: 2
  m_LocalRotation: {x: 0.99518615, y: -0.09800279, z: -5.4517206e-16, w: -5.6145267e-16}
  m_LocalPosition: {x: -0.18151169, y: 1.7763567e-16, z: -3.9502788e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6063050833343931983}
  - {fileID: 23587686796019594}
  m_Father: {fileID: 3279322770728710326}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1368048686636041361
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5852801764460997137}
  serializedVersion: 2
  m_LocalRotation: {x: 0.025480645, y: 0.2768427, z: 0.08803956, w: 0.9565343}
  m_LocalPosition: {x: -0.035140477, y: -0.011706916, z: 0.045055393}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2045972862365378719}
  m_Father: {fileID: 4253435437454317146}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1432097974725868675
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6520780891428513789}
  m_Layer: 0
  m_Name: Finger_01_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1499261050567212161
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9156252830128686354}
  serializedVersion: 2
  m_LocalRotation: {x: -0.57854754, y: 0.6324858, z: -0.3525134, w: -0.37547132}
  m_LocalPosition: {x: -0.058011726, y: -0.041914478, z: -0.07447154}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1959716783922076753}
  m_Father: {fileID: 6063050833343931983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1575069896421504540
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 436700650520038016}
  serializedVersion: 2
  m_LocalRotation: {x: -0.004519649, y: -0.028039929, z: -0.15906802, w: 0.986859}
  m_LocalPosition: {x: -0.03779794, y: -1.4210854e-15, z: 7.105427e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2380969052038965435}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1619095502607631625
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3712053856865962229}
  serializedVersion: 2
  m_LocalRotation: {x: -2.9688968e-16, y: 9.558763e-16, z: 0.04173335, w: 0.9991288}
  m_LocalPosition: {x: -0.111889675, y: -2.1316282e-16, z: -1.095325e-16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5179482426930623893}
  m_Father: {fileID: 6063050833343931983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1818124496288229122
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6063050833343931983}
  m_Layer: 0
  m_Name: Spine_03
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1837086707754797602
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2045972862365378719}
  m_Layer: 0
  m_Name: Thumb_02_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &1879271353928090631
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 77826898412709330}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 8748892135801146194, guid: 6aca1f6740cb34547b89b9013d0e95b3, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1906637715256993764
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2741940703671494884}
  m_Layer: 0
  m_Name: Tome_Attachment_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1938132023569678010
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 892471014829439350}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 3.330669e-16, w: 1}
  m_LocalPosition: {x: -1.4210854e-16, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4253435437454317146}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1959716783922076753
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1105081612389238255}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011328123, y: 0.23685996, z: 0.03380652, w: 0.9708894}
  m_LocalPosition: {x: -0.13197872, y: 0, z: -1.0658141e-16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8587011864611932339}
  m_Father: {fileID: 1499261050567212161}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2024654759063738618
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4901333254034393897}
  m_Layer: 0
  m_Name: Clavicle_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2045972862365378719
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1837086707754797602}
  serializedVersion: 2
  m_LocalRotation: {x: -0.039942026, y: -0.1178403, z: 0.14163579, w: 0.98206806}
  m_LocalPosition: {x: -0.06370214, y: -1.1368684e-15, z: 4.973799e-16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1075521387346685248}
  m_Father: {fileID: 1368048686636041361}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &2092982587477811295
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6107367630474945210}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 3.330669e-16, w: 1}
  m_LocalPosition: {x: -1.4210854e-16, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4253435437454317146}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2148055756556515318
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7772062216398316271}
  m_Layer: 0
  m_Name: UpperLeg_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2194496513092115457
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5856430324347857030}
  m_Layer: 0
  m_Name: Head_Attachment
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2246004350870409590
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1075521387346685248}
  m_Layer: 0
  m_Name: Thumb_03_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2314414036164510084
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7948435603872149950}
  m_Layer: 0
  m_Name: Finger_03_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2354518038949576080
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6245444114836053165}
  serializedVersion: 2
  m_LocalRotation: {x: 0.04177525, y: 0.7058717, z: -0.70419, w: -0.064159356}
  m_LocalPosition: {x: 0.041157685, y: -0.026920697, z: -0.09896705}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 777791358945769269}
  m_Father: {fileID: 6933594010851968246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2359971141924066987
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6266421766440592846}
  m_Layer: 0
  m_Name: Finger_02_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2380969052038965435
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4115184846671197781}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0069121956, y: -0.025717413, z: 0.057002403, w: 0.9980188}
  m_LocalPosition: {x: -0.03791188, y: 0, z: 1.7763568e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1575069896421504540}
  m_Father: {fileID: 3374304926710103320}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2577278881341513857
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 322092966876731958}
  m_Layer: 0
  m_Name: Toes_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2627073087712686349
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4525006086186844242}
  m_Layer: 0
  m_Name: IndexFinger_01_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2689328033685093277
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2801030624185451613}
  m_Layer: 0
  m_Name: IndexFinger_02_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2719471479939252585
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5421446282941359104}
  m_Layer: 0
  m_Name: Prop_Attachment_Extra2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2741940703671494884
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1906637715256993764}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 3.330669e-16, w: 1}
  m_LocalPosition: {x: -1.4210854e-16, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4253435437454317146}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2748577513717367689
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2833795536526263407}
  m_Layer: 0
  m_Name: Thumb_02_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2773961638585825217
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7335264825376003392}
  serializedVersion: 2
  m_LocalRotation: {x: -0.46113664, y: 0.5360532, z: 0.46113664, w: 0.5360532}
  m_LocalPosition: {x: -1.4210854e-16, y: 8.8817837e-17, z: 9.666941e-20}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6933594010851968246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &2801030624185451613
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2689328033685093277}
  serializedVersion: 2
  m_LocalRotation: {x: -0.001153992, y: -0.010741636, z: 0.09832994, w: 0.99509525}
  m_LocalPosition: {x: 0.04093591, y: 0.0000009698692, z: -0.00000004956748}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7785692942529344552}
  m_Father: {fileID: 5411321375219648116}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &2833795536526263407
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2748577513717367689}
  serializedVersion: 2
  m_LocalRotation: {x: -0.039942026, y: -0.1178403, z: 0.14163579, w: 0.98206806}
  m_LocalPosition: {x: 0.06370146, y: -0.000007811825, z: 0.00000043638116}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7294232620968525217}
  m_Father: {fileID: 7985036018500580370}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &2895030391090505734
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1205432933545205584}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011328123, y: 0.23685996, z: 0.03380652, w: 0.9708894}
  m_LocalPosition: {x: 0.1319786, y: 0.0000064733913, z: 0.000000047353424}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 586383688928304559}
  m_Father: {fileID: 4901333254034393897}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3027290942696129956
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 601759044668655081}
  m_Layer: 0
  m_Name: IndexFinger_04_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &3035051052526581440
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6507272962644515495}
  m_Layer: 0
  m_Name: Ball_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &3277173377939324040
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3337379376139191725}
  m_Layer: 0
  m_Name: Ankle_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3279322770728710326
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1160559164006671951}
  serializedVersion: 2
  m_LocalRotation: {x: 0.9950197, y: -0.09967895, z: 1.5402459e-15, w: -1.3039628e-16}
  m_LocalPosition: {x: -0.10393265, y: 7.105427e-17, z: 2.3077684e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1282943702535506445}
  m_Father: {fileID: 6933594010851968246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3337379376139191725
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3277173377939324040}
  serializedVersion: 2
  m_LocalRotation: {x: 0.85767007, y: 0.507213, z: 0.03975271, w: -0.074543595}
  m_LocalPosition: {x: 0.37712327, y: 0.00000013169495, z: -0.0000008411038}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6507272962644515495}
  m_Father: {fileID: 3673790098486218484}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3374304926710103320
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 116487904040327032}
  serializedVersion: 2
  m_LocalRotation: {x: -0.001153992, y: -0.010741636, z: 0.09832994, w: 0.99509525}
  m_LocalPosition: {x: -0.040936317, y: -1.9895197e-15, z: 1.7763568e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2380969052038965435}
  m_Father: {fileID: 4525006086186844242}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3394638535778013530
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6536701477243862240}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7927739812710517610}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3472487845105737143
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7462792986529297168}
  serializedVersion: 2
  m_LocalRotation: {x: -6.1893417e-15, y: -1.8376625e-14, z: -0.2700158, w: 0.96285594}
  m_LocalPosition: {x: -0.112840354, y: -2.797762e-16, z: 7.105427e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8194495803684885590}
  m_Father: {fileID: 3689583888566845469}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3673790098486218484
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6119807523959471549}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6443777, y: 0.02446273, z: -0.0047377087, w: 0.76430136}
  m_LocalPosition: {x: 0.3993092, y: -0.00000038050263, z: 8.881784e-18}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3337379376139191725}
  m_Father: {fileID: 7772062216398316271}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3689583888566845469
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8402072992429664371}
  serializedVersion: 2
  m_LocalRotation: {x: 0.85767007, y: 0.507213, z: 0.03975271, w: -0.074543595}
  m_LocalPosition: {x: -0.3771235, y: 2.4868996e-16, z: 3.907985e-16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3472487845105737143}
  m_Father: {fileID: 777791358945769269}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3696948010103832235
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6345758502909051014}
  serializedVersion: 2
  m_LocalRotation: {x: 0.01664637, y: 0.096525334, z: -0.16913006, w: 0.98071444}
  m_LocalPosition: {x: 0.03003113, y: 0.0000011334713, z: 0.0000003591099}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 219327263403930985}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3712053856865962229
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1619095502607631625}
  m_Layer: 0
  m_Name: Neck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &4115184846671197781
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2380969052038965435}
  m_Layer: 0
  m_Name: IndexFinger_03_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4253435437454317146
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4438490902679172081}
  serializedVersion: 2
  m_LocalRotation: {x: -4.9384195e-17, y: -0.035830766, z: 1.9809905e-15, w: 0.9993579}
  m_LocalPosition: {x: -0.27114528, y: -2.842171e-16, z: -0.00038136425}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8959746552247507148}
  - {fileID: 4525006086186844242}
  - {fileID: 2092982587477811295}
  - {fileID: 1938132023569678010}
  - {fileID: 1368048686636041361}
  - {fileID: 2741940703671494884}
  m_Father: {fileID: 8587011864611932339}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4438490902679172081
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4253435437454317146}
  m_Layer: 0
  m_Name: Hand_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!33 &4463194907307372749
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 77826898412709330}
  m_Mesh: {fileID: -4019297881887596419, guid: 6aca1f6740cb34547b89b9013d0e95b3, type: 3}
--- !u!4 &4525006086186844242
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2627073087712686349}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00028049533, y: 0.065794446, z: 0.004253931, w: 0.99782413}
  m_LocalPosition: {x: -0.10396561, y: 0.003987151, z: 0.026726495}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3374304926710103320}
  m_Father: {fileID: 4253435437454317146}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4568206426714299646
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 658707787793787005}
  - component: {fileID: 6701816942658839231}
  m_Layer: 0
  m_Name: Md_Char_Low_Poly_Man
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &4739525974870579075
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 586383688928304559}
  m_Layer: 0
  m_Name: Elbow_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4846604501395617283
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5897896739745787679}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0026372706, y: 0.019492025, z: 0.021269329, w: 0.99958026}
  m_LocalPosition: {x: -0.042488147, y: -5.684342e-16, z: -4.4408918e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7948435603872149950}
  m_Father: {fileID: 8959746552247507148}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &4901333254034393897
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2024654759063738618}
  serializedVersion: 2
  m_LocalRotation: {x: 0.63248587, y: 0.57854754, z: 0.37547138, w: -0.3525134}
  m_LocalPosition: {x: -0.05801534, y: -0.04191418, z: 0.0744715}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2895030391090505734}
  m_Father: {fileID: 6063050833343931983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5007709532647785133
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7927739812710517610}
  m_Layer: 0
  m_Name: Root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5179482426930623893
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7135721861849013655}
  serializedVersion: 2
  m_LocalRotation: {x: 0.458571, y: -0.5382496, z: 0.458571, w: 0.5382496}
  m_LocalPosition: {x: -0.12164435, y: -4.2632563e-16, z: -1.2212206e-16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8888839494594820989}
  - {fileID: 6360012360348905929}
  - {fileID: 5856430324347857030}
  m_Father: {fileID: 1619095502607631625}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &5384794822977441191
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 981239728439243550}
  serializedVersion: 2
  m_LocalRotation: {x: 0.01664637, y: 0.096525334, z: -0.16913006, w: 0.98071444}
  m_LocalPosition: {x: -0.030032393, y: 1.1368684e-15, z: 7.105427e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7948435603872149950}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &5411321375219648116
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6513905863658712356}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00028049533, y: 0.065794446, z: 0.004253931, w: 0.99782413}
  m_LocalPosition: {x: 0.103966, y: -0.0039899996, z: -0.026726501}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2801030624185451613}
  m_Father: {fileID: 1273017298686451666}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &5421446282941359104
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2719471479939252585}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7927739812710517610}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5726628569288895483
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7785692942529344552}
  m_Layer: 0
  m_Name: IndexFinger_03_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &5852801764460997137
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1368048686636041361}
  m_Layer: 0
  m_Name: Thumb_01_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5856430324347857030
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2194496513092115457}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2.4071082e-15, y: 0.21543543, z: 0.010532023}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5179482426930623893}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5897896739745787679
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4846604501395617283}
  m_Layer: 0
  m_Name: Finger_02_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &5901676795744746584
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7294232620968525217}
  m_Layer: 0
  m_Name: Thumb_03_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &5963168661373436275
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8500721306359367066}
  m_Layer: 0
  m_Name: Prop_Attachment_Extra1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6063050833343931983
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1818124496288229122}
  serializedVersion: 2
  m_LocalRotation: {x: 0.9938103, y: -0.11109032, z: -1.5034843e-16, w: -7.4870264e-16}
  m_LocalPosition: {x: -0.17903724, y: 7.105427e-17, z: 5.915462e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1499261050567212161}
  - {fileID: 4901333254034393897}
  - {fileID: 1619095502607631625}
  m_Father: {fileID: 1282943702535506445}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6107367630474945210
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2092982587477811295}
  m_Layer: 0
  m_Name: Prop_Attachment_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &6119807523959471549
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3673790098486218484}
  m_Layer: 0
  m_Name: LowerLeg_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &6208766190520214154
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6933594010851968246}
  m_Layer: 0
  m_Name: Hips
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &6245444114836053165
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2354518038949576080}
  m_Layer: 0
  m_Name: UpperLeg_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6266421766440592846
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2359971141924066987}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0026372706, y: 0.019492025, z: 0.021269329, w: 0.99958026}
  m_LocalPosition: {x: 0.04248809, y: 0.00000026534414, z: -0.000000019789931}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 219327263403930985}
  m_Father: {fileID: 6520780891428513789}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6325059333875814676
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1273017298686451666}
  m_Layer: 0
  m_Name: Hand_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &6345758502909051014
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3696948010103832235}
  m_Layer: 0
  m_Name: Finger_04_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6360012360348905929
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6860016855441002219}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.09271572, z: 0.12272215}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5179482426930623893}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6402179846189689603
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 777791358945769269}
  m_Layer: 0
  m_Name: LowerLeg_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &6439038932026241779
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1282943702535506445}
  m_Layer: 0
  m_Name: Spine_02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &6461063800580787695
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 219327263403930985}
  m_Layer: 0
  m_Name: Finger_03_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6507272962644515495
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3035051052526581440}
  serializedVersion: 2
  m_LocalRotation: {x: -7.3846406e-14, y: -2.7100105e-13, z: -0.2700158, w: 0.96285594}
  m_LocalPosition: {x: 0.112840325, y: -0.00000001684957, z: -1.7763568e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 322092966876731958}
  m_Father: {fileID: 3337379376139191725}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6513905863658712356
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5411321375219648116}
  m_Layer: 0
  m_Name: IndexFinger_01_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6520780891428513789
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1432097974725868675}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0024864315, y: -0.043879747, z: 0.056519255, w: 0.9974337}
  m_LocalPosition: {x: 0.099825, y: 0.00116, z: 0.032833397}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6266421766440592846}
  m_Father: {fileID: 1273017298686451666}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6536701477243862240
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3394638535778013530}
  m_Layer: 0
  m_Name: Fx_Attachment
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!137 &6701816942658839231
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4568206426714299646}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: -8257928079569425312, guid: 0ad932253e8dc8841948afa5d1a0d051, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 6856591950794880597, guid: 0ad932253e8dc8841948afa5d1a0d051, type: 3}
  m_Bones:
  - {fileID: 6933594010851968246}
  - {fileID: 3279322770728710326}
  - {fileID: 2354518038949576080}
  - {fileID: 7772062216398316271}
  - {fileID: 1282943702535506445}
  - {fileID: 777791358945769269}
  - {fileID: 3673790098486218484}
  - {fileID: 6063050833343931983}
  - {fileID: 3689583888566845469}
  - {fileID: 3337379376139191725}
  - {fileID: 1619095502607631625}
  - {fileID: 1499261050567212161}
  - {fileID: 4901333254034393897}
  - {fileID: 3472487845105737143}
  - {fileID: 6507272962644515495}
  - {fileID: 5179482426930623893}
  - {fileID: 1959716783922076753}
  - {fileID: 2895030391090505734}
  - {fileID: 8194495803684885590}
  - {fileID: 322092966876731958}
  - {fileID: 8587011864611932339}
  - {fileID: 586383688928304559}
  - {fileID: 4253435437454317146}
  - {fileID: 1273017298686451666}
  - {fileID: 1368048686636041361}
  - {fileID: 4525006086186844242}
  - {fileID: 8959746552247507148}
  - {fileID: 7985036018500580370}
  - {fileID: 5411321375219648116}
  - {fileID: 6520780891428513789}
  - {fileID: 2045972862365378719}
  - {fileID: 3374304926710103320}
  - {fileID: 4846604501395617283}
  - {fileID: 2833795536526263407}
  - {fileID: 2801030624185451613}
  - {fileID: 6266421766440592846}
  - {fileID: 1075521387346685248}
  - {fileID: 2380969052038965435}
  - {fileID: 7948435603872149950}
  - {fileID: 7294232620968525217}
  - {fileID: 7785692942529344552}
  - {fileID: 219327263403930985}
  - {fileID: 1575069896421504540}
  - {fileID: 5384794822977441191}
  - {fileID: 601759044668655081}
  - {fileID: 3696948010103832235}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 6933594010851968246}
  m_AABB:
    m_Center: {x: 0.02152279, y: -0.009413511, z: 0}
    m_Extent: {x: 0.9414129, y: 0.2786869, z: 1.0245608}
  m_DirtyAABB: 0
--- !u!1 &6860016855441002219
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6360012360348905929}
  m_Layer: 0
  m_Name: Eyes
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6933594010851968246
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6208766190520214154}
  serializedVersion: 2
  m_LocalRotation: {x: 0.46113664, y: -0.5360532, z: -0.46113664, w: 0.5360532}
  m_LocalPosition: {x: -0, y: 0.8762761, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2773961638585825217}
  - {fileID: 3279322770728710326}
  - {fileID: 7772062216398316271}
  - {fileID: 2354518038949576080}
  m_Father: {fileID: 7927739812710517610}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7120936847584186934
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1282630171075302168}
  m_Layer: 0
  m_Name: Prop_Attachment_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &7135721861849013655
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5179482426930623893}
  m_Layer: 0
  m_Name: Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7294232620968525217
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5901676795744746584}
  serializedVersion: 2
  m_LocalRotation: {x: -0.036403257, y: -0.15203469, z: -0.22999534, w: 0.9605532}
  m_LocalPosition: {x: 0.056655083, y: 0.00000009835502, z: -0.00000020279542}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2833795536526263407}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7335264825376003392
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2773961638585825217}
  m_Layer: 0
  m_Name: Hips_Attachment
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &7403174002692148210
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8959746552247507148}
  m_Layer: 0
  m_Name: Finger_01_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &7462792986529297168
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3472487845105737143}
  m_Layer: 0
  m_Name: Ball_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7772062216398316271
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2148055756556515318}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7058717, y: 0.04177525, z: -0.064159356, w: 0.70419}
  m_LocalPosition: {x: 0.041157782, y: -0.026920749, z: 0.098967105}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3673790098486218484}
  m_Father: {fileID: 6933594010851968246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &7785692942529344552
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5726628569288895483}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0069121956, y: -0.025717413, z: 0.057002403, w: 0.9980188}
  m_LocalPosition: {x: 0.037912127, y: 0.0000032499834, z: -0.00000004545292}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 601759044668655081}
  m_Father: {fileID: 2801030624185451613}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &7927739812710517610
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5007709532647785133}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3394638535778013530}
  - {fileID: 6933594010851968246}
  - {fileID: 8500721306359367066}
  - {fileID: 5421446282941359104}
  m_Father: {fileID: 645631431106755152}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &7948435603872149950
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2314414036164510084}
  serializedVersion: 2
  m_LocalRotation: {x: -0.018149227, y: -0.07147895, z: 0.09251083, w: 0.9929769}
  m_LocalPosition: {x: -0.035133272, y: -1.9895197e-15, z: 9.7699626e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5384794822977441191}
  m_Father: {fileID: 4846604501395617283}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &7985036018500580370
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9144215365492142064}
  serializedVersion: 2
  m_LocalRotation: {x: 0.025480645, y: 0.2768427, z: 0.08803956, w: 0.9565343}
  m_LocalPosition: {x: 0.03514, y: 0.01171, z: -0.045055397}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2833795536526263407}
  m_Father: {fileID: 1273017298686451666}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &8194495803684885590
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1101383286309094527}
  serializedVersion: 2
  m_LocalRotation: {x: 2.983544e-14, y: -0.7071068, z: 2.983544e-14, w: 0.7071068}
  m_LocalPosition: {x: -0.07295756, y: -1.5765166e-16, z: -3.5527136e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3472487845105737143}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8402072992429664371
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3689583888566845469}
  m_Layer: 0
  m_Name: Ankle_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8500721306359367066
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5963168661373436275}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7927739812710517610}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8513380890815333322
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8587011864611932339}
  m_Layer: 0
  m_Name: Elbow_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8587011864611932339
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8513380890815333322}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0016838327, y: 0.040749595, z: -0.041251875, w: 0.99831605}
  m_LocalPosition: {x: -0.33947402, y: -0.0014490709, z: -0.0014362641}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4253435437454317146}
  m_Father: {fileID: 1959716783922076753}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8711456514701446424
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8888839494594820989}
  m_Layer: 0
  m_Name: Eyebrows
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8888839494594820989
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8711456514701446424}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.1276692, z: 0.12272215}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5179482426930623893}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &8959746552247507148
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7403174002692148210}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0024864315, y: -0.043879747, z: 0.056519255, w: 0.9974337}
  m_LocalPosition: {x: -0.099825256, y: -0.0011595894, z: -0.0328334}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4846604501395617283}
  m_Father: {fileID: 4253435437454317146}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9144215365492142064
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7985036018500580370}
  m_Layer: 0
  m_Name: Thumb_01_R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &9156252830128686354
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1499261050567212161}
  m_Layer: 0
  m_Name: Clavicle_L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 1755795515}
  - {fileID: 742561191}
  - {fileID: 2023603159}
  - {fileID: 1675357203}
  - {fileID: 630081068}
  - {fileID: 509783886}
  - {fileID: 1109713187}
  - {fileID: 1269507150}
  - {fileID: 195219231}
  - {fileID: 1820596739}
  - {fileID: 973939543}
