using UnityEngine;
using ns.Value;
using ns.Character;
using EnemyAIBase;

namespace EnemyAIBase
{
    /// <summary>
    /// 伤害计算分析器 - 用于分析和显示当前伤害计算的理论值
    /// </summary>
    public class DamageCalculationAnalyzer : MonoBehaviour
    {
        [Header("分析目标")]
        [<PERSON><PERSON><PERSON>("攻击者（敌人）")]
        public EnemyInfo attacker;
        
        [<PERSON><PERSON><PERSON>("防御者（玩家）")]
        public CharacterInfo defender;

        [<PERSON><PERSON>("分析结果")]
        [SerializeField, ReadOnly] private float weaponPhysicalATK;
        [SerializeField, ReadOnly] private float actionMultiplier;
        [SerializeField, ReadOnly] private float physicalDamage;
        [SerializeField, ReadOnly] private float allBaseDamages;
        [SerializeField, ReadOnly] private float defenseValue;
        [SerializeField, ReadOnly] private float defenseRate;
        [SerializeField, ReadOnly] private float postDefenseDamage;
        [Serial<PERSON>Field, ReadOnly] private float resistanceValue;
        [SerializeField, ReadOnly] private float reductionRate;
        [Serial<PERSON><PERSON><PERSON>, ReadOnly] private float finalAttributeDamage;
        [SerializeField, ReadOnly] private float criticalCoefficient;
        [SerializeField, ReadOnly] private float allDamages;
        [SerializeField, ReadOnly] private float defenderCoefficient;
        [SerializeField, ReadOnly] private float damageFloatFactor;
        [SerializeField, ReadOnly] private float ruleDamage;
        [SerializeField, ReadOnly] private int finalDamage;

        [Header("计算参数")]
        [SerializeField, ReadOnly] private bool isDefenderInPreOrRecovery;

        private void Start()
        {
            if (attacker == null) attacker = GetComponent<EnemyInfo>();
            if (defender == null) defender = GameObject.FindGameObjectWithTag("Player")?.GetComponent<CharacterInfo>();
        }

        [ContextMenu("分析伤害计算")]
        public void AnalyzeDamageCalculation()
        {
            if (attacker == null || defender == null)
            {
                Debug.LogError("缺少攻击者或防御者！");
                return;
            }

            Debug.Log("=== 开始伤害计算分析 ===");

            // 1. 获取武器基础物理攻击力
            weaponPhysicalATK = attacker.GetWeaponPhysicalATK();
            Debug.Log($"1. 武器基础物理攻击力: {weaponPhysicalATK}");

            // 2. 获取动作倍率
            var attackerMovtionInfo = attacker.MovtionManager?.GetMovtionInfo(attacker.AttackMovtionID);
            if (attackerMovtionInfo != null)
            {
                actionMultiplier = attackerMovtionInfo.ActionMultiplier;
                Debug.Log($"2. 动作倍率: {actionMultiplier} (动作ID: {attacker.AttackMovtionID})");
            }
            else
            {
                actionMultiplier = 1f; // 默认值
                Debug.LogWarning($"2. 找不到动作ID {attacker.AttackMovtionID}，使用默认倍率: {actionMultiplier}");
            }

            // 3. 物理部分伤害
            physicalDamage = (weaponPhysicalATK * GlobalConstants.PhysicalDamageCorrectionFactor) * actionMultiplier;
            Debug.Log($"3. 物理部分伤害: {weaponPhysicalATK} × {GlobalConstants.PhysicalDamageCorrectionFactor} × {actionMultiplier} = {physicalDamage}");

            // 4. 基本伤害总值（暂时忽略属性伤害）
            allBaseDamages = physicalDamage; // + otherDamages (目前为0)
            Debug.Log($"4. 基本伤害总值: {allBaseDamages}");

            // 5. 防御力计算
            defenseValue = defender.GetDEF();
            defenseRate = defenseValue / (GlobalConstants.ReducedDamageRateDamageFactor * allBaseDamages
                + GlobalConstants.DamageReductionRateDefenseFactor * defenseValue);
            defenseRate = Mathf.Min(defenseRate, GlobalConstants.DefenseRateCeiling);
            Debug.Log($"5. 防御力: {defenseValue}, 防御减伤率: {defenseRate:F3}");

            // 6. 防御后伤害
            postDefenseDamage = allBaseDamages * (1 - Mathf.Min(defenseRate, 0.9f));
            Debug.Log($"6. 防御后伤害: {allBaseDamages} × (1 - {Mathf.Min(defenseRate, 0.9f):F3}) = {postDefenseDamage}");

            // 7. 抗性计算
            var physicalResistanceType = attackerMovtionInfo?.PhysicalResistanceType ?? ResistanceType.普通;
            resistanceValue = defender.GetResistance(physicalResistanceType);
            reductionRate = resistanceValue / (resistanceValue + GlobalConstants.AttributeReductionRateAdjustmentFactor);
            finalAttributeDamage = postDefenseDamage * (1 - reductionRate);
            Debug.Log($"7. 抗性值: {resistanceValue}, 抗性减伤率: {reductionRate:F3}, 最终属性伤害: {finalAttributeDamage}");

            // 8. 临界状态效果系数
            criticalCoefficient = attacker.GetCriticalStateEffectCoefficient();
            allDamages = finalAttributeDamage * criticalCoefficient;
            Debug.Log($"8. 临界状态系数: {criticalCoefficient}, 总伤害: {allDamages}");

            // 9. 反击系数
            isDefenderInPreOrRecovery = defender.IsInPreMovtionFlag || defender.IsInMovtionRecoveryFlag;
            if (isDefenderInPreOrRecovery)
            {
                defenderCoefficient = (GlobalConstants.DefenderFactorFloor + GlobalConstants.DefenderFactorCeiling) / 2f; // 平均值
                Debug.Log($"9. 防御者处于前摇/后摇，反击系数: {defenderCoefficient:F2} (范围: {GlobalConstants.DefenderFactorFloor}-{GlobalConstants.DefenderFactorCeiling})");
            }
            else
            {
                defenderCoefficient = 1f;
                Debug.Log($"9. 防御者不在前摇/后摇，反击系数: {defenderCoefficient}");
            }

            // 10. 伤害浮动系数
            damageFloatFactor = (GlobalConstants.DamageFloatFactorFloor + GlobalConstants.DamageFloatFactorCeiling) / 2f; // 平均值
            Debug.Log($"10. 伤害浮动系数: {damageFloatFactor:F2} (范围: {GlobalConstants.DamageFloatFactorFloor}-{GlobalConstants.DamageFloatFactorCeiling})");

            // 11. 常规伤害
            ruleDamage = allDamages * defenderCoefficient * damageFloatFactor;
            Debug.Log($"11. 常规伤害: {allDamages} × {defenderCoefficient:F2} × {damageFloatFactor:F2} = {ruleDamage}");

            // 12. 最终伤害（忽略处决伤害）
            finalDamage = Mathf.FloorToInt(ruleDamage);
            Debug.Log($"12. 最终伤害: {finalDamage}");

            Debug.Log("=== 伤害计算分析完成 ===");

            // 显示伤害范围
            CalculateDamageRange();
        }

        private void CalculateDamageRange()
        {
            Debug.Log("=== 伤害范围分析 ===");

            // 最小伤害（无反击系数，最小浮动）
            float minDamage = allDamages * 1f * GlobalConstants.DamageFloatFactorFloor;
            
            // 最大伤害（有反击系数，最大浮动）
            float maxDamage = allDamages * GlobalConstants.DefenderFactorCeiling * GlobalConstants.DamageFloatFactorCeiling;

            Debug.Log($"伤害范围: {Mathf.FloorToInt(minDamage)} - {Mathf.FloorToInt(maxDamage)}");
            Debug.Log($"平均伤害: {finalDamage}");
        }

        [ContextMenu("显示当前配置")]
        public void ShowCurrentConfiguration()
        {
            if (attacker == null || defender == null)
            {
                Debug.LogError("缺少攻击者或防御者！");
                return;
            }

            Debug.Log("=== 当前配置信息 ===");
            Debug.Log($"攻击者: {attacker.name}");
            Debug.Log($"  - 基础攻击力: {attacker.BaseAttackPower}");
            Debug.Log($"  - 攻击动作ID: {attacker.AttackMovtionID}");
            Debug.Log($"  - 基础削韧值: {attacker.BaseReducedPoiseValue}");

            Debug.Log($"防御者: {defender.name}");
            Debug.Log($"  - 血量: {defender.HP}");
            Debug.Log($"  - 防御力: {defender.GetDEF()}");
            Debug.Log($"  - 普通抗性: {defender.GetResistance(ResistanceType.普通)}");

            Debug.Log("全局常数:");
            Debug.Log($"  - 物理攻击修正系数: {GlobalConstants.PhysicalDamageCorrectionFactor}");
            Debug.Log($"  - 防御减伤率伤害系数A: {GlobalConstants.ReducedDamageRateDamageFactor}");
            Debug.Log($"  - 防御减伤率防御系数B: {GlobalConstants.DamageReductionRateDefenseFactor}");
            Debug.Log($"  - 属性减伤率调整系数C: {GlobalConstants.AttributeReductionRateAdjustmentFactor}");
            Debug.Log($"  - 反击系数范围: {GlobalConstants.DefenderFactorFloor} - {GlobalConstants.DefenderFactorCeiling}");
            Debug.Log($"  - 伤害浮动系数范围: {GlobalConstants.DamageFloatFactorFloor} - {GlobalConstants.DamageFloatFactorCeiling}");
        }

        [ContextMenu("模拟实际伤害计算")]
        public void SimulateActualDamageCalculation()
        {
            if (attacker == null || defender == null)
            {
                Debug.LogError("缺少攻击者或防御者！");
                return;
            }

            Debug.Log("=== 模拟实际伤害计算 ===");
            
            var damageContext = new DamageContext(attacker);
            int actualDamage = DamageCalculator.CalculateDamage(attacker, defender);
            
            Debug.Log($"实际计算伤害: {actualDamage}");
            Debug.Log($"理论平均伤害: {finalDamage}");
            Debug.Log($"差异: {actualDamage - finalDamage}");
        }
    }

    // 用于在Inspector中显示只读字段的属性
    public class ReadOnlyAttribute : PropertyAttribute { }

#if UNITY_EDITOR
    [UnityEditor.CustomPropertyDrawer(typeof(ReadOnlyAttribute))]
    public class ReadOnlyDrawer : UnityEditor.PropertyDrawer
    {
        public override void OnGUI(Rect position, UnityEditor.SerializedProperty property, GUIContent label)
        {
            GUI.enabled = false;
            UnityEditor.EditorGUI.PropertyField(position, property, label, true);
            GUI.enabled = true;
        }
    }
#endif
}
